import React, { useState, useMemo, useEffect } from 'react';
import { <PERSON>, Baby, Shield as Child, UserPlus, Car, Search, Filter, Home, ChevronDown, Calculator, DollarSign, Download, BarChart3, Info, Package, TrendingUp } from 'lucide-react';
import { supabase } from '../lib/supabaseClient';
import { exportFamilyTypePricing, calculatePricingInsights } from '../utils/familyPriceExport';
import { fetchCompleteQuoteData, calculateFamilyTypePriceDetailed } from '../utils/familyPriceCalculator';
import {
  calculateFamilyTypePackageCost,
  fetchQuoteMappingData,
  fetchBaselineQuoteData,
  formatPrice
} from '../utils/familyTypePackageCalculator';

const FAMILY_OPTIONS = {
  adults: [1, 2, 3, 4, 5, 6],
  infants: [0, 1, 2, 3],
  children: [0, 1, 2, 3, 4],
  teenagers: [0, 1, 2, 3, 4],
  grandparents: [0, 1, 2, 3, 4],
  rooms: [1, 2, 3, 4, 5],
  cabTypes: [
    'Sedan - 4 Seater',
    'Toyota Innova AC',
    'SUV',
    'Tempo Traveller',
    'Mini Bus'
  ]
};

interface FamilyType {
  id: string;
  name: string;
  description: string;
  adults: number;
  infants: number;
  children: number;
  teenagers: number;
  grandparents: number;
  totalCount: number;
  cabType: string;
  rooms: number;
  family_id?: string;
  family_type?: string;
  no_of_adults?: number;
  no_of_infants?: number;
  no_of_child?: number;
  no_of_children?: number;
  family_count?: number;
  cab_type?: string;
  cab_capacity?: number;
  rooms_need?: number;
  calculatedPrice?: number;
  packageCost?: number;
  priceBreakdown?: {
    hotelCost: number;
    otherCosts: number;
    commission: number;
    gst: number;
    discount: number;
    roomsNeeded: number;
    extraAdults: number;
    childrenCharged: number;
    infantsFree: number;
  };
  packageBreakdown?: {
    roomCost: number;
    extraAdultCost: number;
    childrenCost: number;
    infantCost: number;
    vehicleCost: number;
    additionalCosts: number;
    subtotal: number;
    commission: number;
    gst: number;
    totalPackageCost: number;
    roomsNeeded: number;
    extraAdultsCharged: number;
    childrenCharged: number;
    infantsIncluded: number;
  };
}

interface BaselineQuote {
  id: string;
  package_name: string;
  customer_name: string;
  destination: string;
  family_type: string;
  total_cost: number;
  no_of_persons: number;
  children: number;
  infants: number;
  extra_adults: number;
  is_draft: boolean;
}

// Default family types (fallback if Supabase data is not available)
const defaultFamilyTypes: FamilyType[] = [
  {
    id: 'BB-1',
    name: 'Baby Bliss',
    description: '2 Adults + 1 Infant (Below 2 yrs)',
    adults: 2,
    infants: 1,
    children: 0,
    teenagers: 0,
    grandparents: 0,
    totalCount: 3,
    cabType: 'Sedan - 4 Seater',
    rooms: 1
  },
  {
    id: 'TD-2',
    name: 'Tiny Delight',
    description: '2 Adults + 1 Child (Below 5 yrs)',
    adults: 2,
    infants: 0,
    children: 1,
    teenagers: 0,
    grandparents: 0,
    totalCount: 3,
    cabType: 'Sedan - 4 Seater',
    rooms: 1
  },
  {
    id: 'GB-15',
    name: 'Grand Bliss',
    description: '2 Adults + 1 Infant (Below 2 yrs) + 2 Grandparents',
    adults: 2,
    infants: 1,
    children: 0,
    teenagers: 0,
    grandparents: 2,
    totalCount: 5,
    cabType: 'Toyota Innova AC',
    rooms: 2
  }
];

function FamilyTypeTab() {
  const [selectedFamily, setSelectedFamily] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [familyTypes, setFamilyTypes] = useState<FamilyType[]>(defaultFamilyTypes);
  const [baselineQuotes, setBaselineQuotes] = useState<BaselineQuote[]>([]);
  const [selectedBaselineQuote, setSelectedBaselineQuote] = useState<string>('');
  const [isLoadingFamilyTypes, setIsLoadingFamilyTypes] = useState(false);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState(false);
  const [showPriceCalculation, setShowPriceCalculation] = useState(false);
  const [includeDrafts, setIncludeDrafts] = useState(true);
  const [isCalculatingPackageCosts, setIsCalculatingPackageCosts] = useState(false);
  const [showPackageCalculation, setShowPackageCalculation] = useState(false);
  const [filters, setFilters] = useState({
    maxMembers: 'all',
    vehicleType: 'all',
    hasInfants: false,
    hasGrandparents: false
  });

  const [newFamilyType, setNewFamilyType] = useState({
    adults: 2,
    infants: 0,
    children: 0,
    teenagers: 0,
    grandparents: 0,
    rooms: 1,
    cabType: FAMILY_OPTIONS.cabTypes[0]
  });

  // Fetch family types from Supabase
  useEffect(() => {
    fetchFamilyTypes();
    fetchBaselineQuotes();
  }, [supabase]);

  // Refetch baseline quotes when includeDrafts toggle changes
  useEffect(() => {
    if (supabase) {
      fetchBaselineQuotes();
    }
  }, [includeDrafts, supabase]);

  const fetchFamilyTypes = async () => {
    setIsLoadingFamilyTypes(true);
    try {
      if (!supabase) {
        console.error('Supabase client not available');
        return;
      }

      const { data, error } = await supabase
        .from('family_type')
        .select('*')
        .order('family_id');

      if (error) {
        console.error('Error fetching family types:', error);
        return;
      }

      if (data && data.length > 0) {
        // Transform Supabase data to match our interface
        const transformedFamilyTypes: FamilyType[] = data.map(item => ({
          id: item.family_id || `FT-${Math.random().toString(36).substr(2, 9)}`,
          name: item.family_type || 'Unknown Family Type',
          description: item.family_type || 'Unknown Family Type',
          adults: item.no_of_adults || 0,
          infants: item.no_of_infants || 0,
          children: (item.no_of_child || 0) + (item.no_of_children || 0), // Combine both child fields
          teenagers: 0, // Not available in current schema
          grandparents: 0, // Not available in current schema
          totalCount: item.family_count || 0,
          cabType: item.cab_type || 'Standard',
          rooms: item.rooms_need || 1,
          // Keep original data for reference
          family_id: item.family_id,
          family_type: item.family_type,
          no_of_adults: item.no_of_adults,
          no_of_infants: item.no_of_infants,
          no_of_child: item.no_of_child,
          no_of_children: item.no_of_children,
          family_count: item.family_count,
          cab_type: item.cab_type,
          cab_capacity: item.cab_capacity,
          rooms_need: item.rooms_need
        }));

        setFamilyTypes(transformedFamilyTypes);
      }
    } catch (error) {
      console.error('Exception fetching family types:', error);
    } finally {
      setIsLoadingFamilyTypes(false);
    }
  };

  const fetchBaselineQuotes = async () => {
    setIsLoadingQuotes(true);
    try {
      if (!supabase) {
        console.error('Supabase client not available');
        return;
      }

      let query = supabase
        .from('quotes')
        .select('id, package_name, customer_name, destination, family_type, total_cost, no_of_persons, children, infants, extra_adults, is_draft')
        .not('total_cost', 'is', null)
        .gt('total_cost', 0)
        .order('created_at', { ascending: false })
        .limit(20);

      // Only filter out drafts if includeDrafts is false
      if (!includeDrafts) {
        query = query.eq('is_draft', false);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching baseline quotes:', error);
        return;
      }

      setBaselineQuotes(data || []);
    } catch (error) {
      console.error('Exception fetching baseline quotes:', error);
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  // Calculate price for a family type based on baseline quote
  const calculateFamilyTypePrice = (familyType: FamilyType, baselineQuote: BaselineQuote): number => {
    if (!baselineQuote || !baselineQuote.total_cost) return 0;

    // Get baseline family composition
    const baselineAdults = (baselineQuote.no_of_persons || 0) + (baselineQuote.extra_adults || 0);
    const baselineChildren = baselineQuote.children || 0;
    const baselineInfants = baselineQuote.infants || 0;
    const baselineTotalPax = baselineAdults + baselineChildren + baselineInfants;

    // Ensure baseline has at least some people
    if (baselineTotalPax === 0) return 0;

    // Get target family composition
    const targetAdults = familyType.adults || familyType.no_of_adults || 0;
    const targetChildren = familyType.children || familyType.no_of_children || familyType.no_of_child || 0;
    const targetInfants = familyType.infants || familyType.no_of_infants || 0;
    const targetTotalPax = targetAdults + targetChildren + targetInfants;

    // Ensure target family has at least some people
    if (targetTotalPax === 0) return 0;

    // Base price per adult from baseline (using adults as base unit)
    const basePricePerAdult = baselineAdults > 0 ? baselineQuote.total_cost / baselineAdults : baselineQuote.total_cost / baselineTotalPax;

    // Calculate pricing factors
    let adultPrice = basePricePerAdult * targetAdults;
    let childPrice = basePricePerAdult * 0.7 * targetChildren; // 70% of adult price for children
    let infantPrice = basePricePerAdult * 0.1 * targetInfants; // 10% of adult price for infants

    // Room factor adjustment
    const baselineRooms = Math.max(1, Math.ceil(baselineAdults / 2)); // Assume 2 adults per room, minimum 1 room
    const targetRooms = familyType.rooms || familyType.rooms_need || Math.max(1, Math.ceil(targetAdults / 2));
    const roomFactor = targetRooms / baselineRooms;

    // Apply room factor to accommodation portion (assume 40% of total cost is accommodation)
    const baseTotal = adultPrice + childPrice + infantPrice;
    const accommodationPortion = baseTotal * 0.4;
    const otherPortion = baseTotal * 0.6;
    
    const adjustedAccommodation = accommodationPortion * roomFactor;
    const totalPrice = adjustedAccommodation + otherPortion;

    // Add transportation cost adjustment based on cab type
    let transportationMultiplier = 1.0;
    const familyCabType = (familyType.cabType || familyType.cab_type || '').toLowerCase();
    
    if (familyCabType.includes('sedan')) {
      transportationMultiplier = 1.0; // Base price
    } else if (familyCabType.includes('innova') || familyCabType.includes('suv')) {
      transportationMultiplier = 1.15; // 15% increase
    } else if (familyCabType.includes('tempo traveller')) {
      transportationMultiplier = 1.3; // 30% increase
    } else if (familyCabType.includes('mini bus')) {
      transportationMultiplier = 1.5; // 50% increase
    }

    const finalPrice = totalPrice * transportationMultiplier;
    return Math.round(Math.max(0, finalPrice)); // Ensure non-negative result
  };

  // Calculate package costs for all family types using Quote Mapping data
  const calculatePackageCosts = async () => {
    if (!selectedBaselineQuote) {
      alert('Please select a customer\'s baseline quote first');
      return;
    }

    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
    if (!baselineQuote) {
      alert('Selected quote not found');
      return;
    }

    setIsCalculatingPackageCosts(true);

    try {
      console.log('Fetching quote mapping data for package cost calculation...');

      // Fetch quote mapping data
      const quoteMappingData = await fetchQuoteMappingData(selectedBaselineQuote);
      if (!quoteMappingData) {
        alert('Quote mapping data not found. Please ensure the quote has been mapped in the Quote Mapping tab first.');
        return;
      }

      // Fetch baseline quote data
      const baselineQuoteData = await fetchBaselineQuoteData(selectedBaselineQuote);
      if (!baselineQuoteData) {
        alert('Could not fetch baseline quote data');
        return;
      }

      console.log('Calculating package costs for all family types...');

      // Calculate package costs for each family type
      const updatedFamilyTypes = familyTypes.map(familyType => {
        console.log(`Calculating package cost for ${familyType.family_type || familyType.name}...`);

        const packageBreakdown = calculateFamilyTypePackageCost(
          familyType,
          baselineQuoteData,
          quoteMappingData
        );

        console.log(`Package cost for ${familyType.name}:`, packageBreakdown);

        return {
          ...familyType,
          packageCost: packageBreakdown.totalPackageCost,
          packageBreakdown
        };
      });

      setFamilyTypes(updatedFamilyTypes);
      setShowPackageCalculation(true);

      const calculatedCount = updatedFamilyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length;
      console.log(`Successfully calculated package costs for ${calculatedCount} family types`);

    } catch (error) {
      console.error('Error calculating package costs:', error);
      alert('Error calculating package costs. Please try again.');
    } finally {
      setIsCalculatingPackageCosts(false);
    }
  };

  // Calculate prices for all family types using enhanced logic
  const calculateAllPrices = async () => {
    if (!selectedBaselineQuote) {
      alert('Please select a customer\'s baseline quote first');
      return;
    }

    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
    if (!baselineQuote) {
      alert('Selected quote not found');
      return;
    }

    try {
      console.log('Fetching complete quote data for:', baselineQuote.customer_name);
      
      // Fetch complete quote data including hotel rows and costs
      const completeQuoteData = await fetchCompleteQuoteData(selectedBaselineQuote);
      
      if (!completeQuoteData) {
        alert('Could not fetch complete quote data. Using simplified calculation.');
        // Fallback to simple calculation
        const updatedFamilyTypes = familyTypes.map(familyType => {
          const calculatedPrice = calculateFamilyTypePrice(familyType, baselineQuote);
          return {
            ...familyType,
            calculatedPrice
          };
        });
        setFamilyTypes(updatedFamilyTypes);
        setShowPriceCalculation(true);
        return;
      }

      console.log('Using enhanced calculation with complete quote data');

      // Calculate prices using the enhanced calculator
      const updatedFamilyTypes = familyTypes.map(familyType => {
        console.log(`\n=== Calculating for ${familyType.family_type} ===`);
        console.log('Family Data:', {
          adults: familyType.no_of_adults || familyType.adults,
          children: familyType.no_of_children || familyType.children,
          infants: familyType.no_of_infants || familyType.infants,
          cabType: familyType.cab_type || familyType.cabType,
          cabCapacity: familyType.cab_capacity,
          roomsNeed: familyType.rooms_need || familyType.rooms
        });
        
        const calculation = calculateFamilyTypePriceDetailed(familyType, completeQuoteData);
        console.log(`Enhanced price for ${familyType.name}:`, calculation);
        console.log('Breakdown:', calculation.breakdown);
        
        return {
          ...familyType,
          calculatedPrice: calculation.totalPrice,
          priceBreakdown: calculation.breakdown
        };
      });

      setFamilyTypes(updatedFamilyTypes);
      setShowPriceCalculation(true);

      // Show success message with baseline quote details
      const calculatedCount = updatedFamilyTypes.filter(ft => ft.calculatedPrice && ft.calculatedPrice > 0).length;
      console.log(`\n=== CALCULATION SUMMARY ===`);
      console.log(`Successfully calculated enhanced prices for ${calculatedCount} family types based on ${baselineQuote.customer_name}'s quote`);
      console.log('Baseline Quote:', {
        customer: baselineQuote.customer_name,
        total: baselineQuote.total_cost,
        adults: (baselineQuote.no_of_persons || 0) + (baselineQuote.extra_adults || 0),
        children: baselineQuote.children,
        infants: baselineQuote.infants
      });
      
    } catch (error) {
      console.error('Error in enhanced calculation:', error);
      alert('Error calculating prices. Please try again.');
    }
  };

  const filteredFamilyTypes = useMemo(() => {
    return familyTypes.filter(type => {
      const matchesSearch = type.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          type.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          type.id.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesMaxMembers = filters.maxMembers === 'all' || type.totalCount <= parseInt(filters.maxMembers);
      const matchesVehicleType = filters.vehicleType === 'all' || 
                                 (type.cabType && type.cabType.includes(filters.vehicleType)) ||
                                 (type.cab_type && type.cab_type.includes(filters.vehicleType));
      const matchesInfants = !filters.hasInfants || type.infants > 0;
      const matchesGrandparents = !filters.hasGrandparents || type.grandparents > 0;

      return matchesSearch && matchesMaxMembers && matchesVehicleType && matchesInfants && matchesGrandparents;
    });
  }, [searchQuery, filters, familyTypes]);

  const handleAddNewFamily = () => {
    const totalCount = newFamilyType.adults + newFamilyType.infants + 
                      newFamilyType.children + newFamilyType.teenagers + 
                      newFamilyType.grandparents;
    
    const newFamily: FamilyType = {
      id: `FT-${Math.random().toString(36).substr(2, 9)}`,
      name: "Custom Family",
      description: `${newFamilyType.adults} Adults${newFamilyType.children ? ` + ${newFamilyType.children} Children` : ''}${newFamilyType.infants ? ` + ${newFamilyType.infants} Infants` : ''}${newFamilyType.grandparents ? ` + ${newFamilyType.grandparents} Grandparents` : ''}`,
      ...newFamilyType,
      totalCount
    };
    
    setFamilyTypes([...familyTypes, newFamily]);
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-green-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Package className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">Family Type Package Pricing</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Calculate accurate package costs for all family types using Quote Mapping data
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">Total Family Types</div>
                <div className="text-2xl font-bold text-blue-600">{familyTypes.length}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Quick Stats */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-blue-600">Family Types</div>
                  <div className="text-xl font-bold text-blue-800">{familyTypes.length}</div>
                </div>
              </div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm text-green-600">Package Costs</div>
                  <div className="text-xl font-bold text-green-800">
                    {familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm text-purple-600">Baseline Quotes</div>
                  <div className="text-xl font-bold text-purple-800">{baselineQuotes.length}</div>
                </div>
              </div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-orange-600">Status</div>
                  <div className="text-sm font-bold text-orange-800">
                    {isCalculatingPackageCosts ? 'Calculating...' : 'Ready'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Baseline Quote Selection */}
          <div className="mb-6 bg-white border border-gray-200 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <Calculator className="h-5 w-5 text-blue-600" />
              Package Cost Calculator
            </h4>

            {baselineQuotes.length === 0 && !isLoadingQuotes ? (
              <div className="p-4 bg-orange-50 border border-orange-200 rounded-md">
                <div className="flex items-center gap-3">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-orange-800">No Baseline Quotes Available</h3>
                    <p className="text-sm text-orange-700 mt-1">
                      Create a quote in Quote Generator and map it in Quote Mapping tab to calculate package costs.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Baseline Quote for Calculation
                  </label>

                  <select
                    value={selectedBaselineQuote}
                    onChange={(e) => setSelectedBaselineQuote(e.target.value)}
                    className="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:ring-blue-500 focus:border-blue-500"
                    disabled={isLoadingQuotes}
                  >
                    <option value="">Choose a customer's baseline quote...</option>
                    {baselineQuotes.map(quote => (
                      <option key={quote.id} value={quote.id}>
                        {quote.customer_name} - {quote.destination} ({quote.family_type}) - {formatPrice(quote.total_cost)}
                      </option>
                    ))}
                  </select>

                  {selectedBaselineQuote && (
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      {(() => {
                        const selectedQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                        if (!selectedQuote) return null;

                        return (
                          <div className="text-sm">
                            <p className="font-medium text-blue-800">Baseline: {selectedQuote.customer_name}</p>
                            <p className="text-blue-700">{selectedQuote.destination} • {selectedQuote.family_type} • {formatPrice(selectedQuote.total_cost)}</p>
                          </div>
                        );
                      })()}
                    </div>
                  )}
                </div>

                <div className="flex flex-col justify-end space-y-3">
                  <button
                    onClick={calculatePackageCosts}
                    disabled={!selectedBaselineQuote || isCalculatingPackageCosts}
                    className="w-full px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                  >
                    <Package className="h-5 w-5" />
                    {isCalculatingPackageCosts ? 'Calculating...' : 'Calculate Package Costs'}
                  </button>

                  <div className="text-xs text-gray-500 text-center">
                    Uses Quote Mapping data for accurate pricing
                  </div>
                </div>
              </div>
            )}
          </div>

          {showPackageCalculation && selectedBaselineQuote && (
            <div className="mt-6 space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center gap-2 text-green-800">
                  <Package className="h-5 w-5" />
                  <span className="font-medium">Package cost calculation completed!</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  {(() => {
                    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    const calculatedCount = familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length;
                    return `Package costs calculated for ${calculatedCount} family types using Quote Mapping data from ${baselineQuote?.customer_name || 'selected customer'}'s quote for ${baselineQuote?.destination || 'destination'} (${formatPrice(baselineQuote?.total_cost || 0)}).`;
                  })()}
                </p>
              </div>

              {/* Package Cost Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <Package className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="text-sm text-green-600">Calculated</div>
                      <div className="text-xl font-bold text-green-800">
                        {familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).length}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="text-sm text-blue-600">Avg Cost</div>
                      <div className="text-xl font-bold text-blue-800">
                        {(() => {
                          const costs = familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).map(ft => ft.packageCost || 0);
                          const avg = costs.length > 0 ? costs.reduce((a, b) => a + b, 0) / costs.length : 0;
                          return formatPrice(Math.round(avg));
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-purple-600" />
                    <div>
                      <div className="text-sm text-purple-600">Min Cost</div>
                      <div className="text-xl font-bold text-purple-800">
                        {(() => {
                          const costs = familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).map(ft => ft.packageCost || 0);
                          const min = costs.length > 0 ? Math.min(...costs) : 0;
                          return formatPrice(min);
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-orange-600" />
                    <div>
                      <div className="text-sm text-orange-600">Max Cost</div>
                      <div className="text-xl font-bold text-orange-800">
                        {(() => {
                          const costs = familyTypes.filter(ft => ft.packageCost && ft.packageCost > 0).map(ft => ft.packageCost || 0);
                          const max = costs.length > 0 ? Math.max(...costs) : 0;
                          return formatPrice(max);
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {showPriceCalculation && selectedBaselineQuote && (
            <div className="mt-6 space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center gap-2 text-blue-800">
                  <DollarSign className="h-5 w-5" />
                  <span className="font-medium">Basic price calculation completed!</span>
                </div>
                <p className="text-sm text-blue-700 mt-1">
                  {(() => {
                    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    const calculatedCount = familyTypes.filter(ft => ft.calculatedPrice && ft.calculatedPrice > 0).length;
                    return `Basic prices have been calculated for ${calculatedCount} family types based on ${baselineQuote?.customer_name || 'selected customer'}'s quote for ${baselineQuote?.destination || 'destination'} (${formatPrice(baselineQuote?.total_cost || 0)}). For more accurate pricing, use the Package Cost calculation above.`;
                  })()}
                </p>
              </div>

              {/* Pricing Insights */}
              {(() => {
                const insights = calculatePricingInsights(familyTypes);
                if (!insights) return null;
                
                return (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-center gap-2 text-blue-800 mb-3">
                      <BarChart3 className="h-5 w-5" />
                      <span className="font-medium">Pricing Insights</span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-blue-600 font-medium">Total Family Types</p>
                        <p className="text-blue-800">{insights.totalFamilyTypes}</p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Price Range</p>
                        <p className="text-blue-800">{formatPrice(insights.minPrice)} - {formatPrice(insights.maxPrice)}</p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Average Price</p>
                        <p className="text-blue-800">{formatPrice(insights.avgPrice)}</p>
                      </div>
                      <div>
                        <p className="text-blue-600 font-medium">Most/Least Expensive</p>
                        <p className="text-blue-800 text-xs">{insights.maxPriceFamily} / {insights.minPriceFamily}</p>
                      </div>
                    </div>
                  </div>
                );
              })()}

              {/* Export Button */}
              <div className="flex justify-end">
                <button
                  onClick={() => {
                    const baselineQuote = baselineQuotes.find(q => q.id === selectedBaselineQuote);
                    exportFamilyTypePricing(familyTypes, baselineQuote || null);
                  }}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Pricing Data (CSV)
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Search family types..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          
          <div className="flex gap-2">
              <select
                className="block w-full md:w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={filters.maxMembers}
                onChange={(e) => setFilters(prev => ({ ...prev, maxMembers: e.target.value }))}
              >
                <option value="all">All Sizes</option>
                <option value="3">Up to 3 members</option>
                <option value="4">Up to 4 members</option>
                <option value="5">Up to 5 members</option>
                <option value="6">Up to 6 members</option>
              </select>
              <select
                className="block w-full md:w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                value={filters.vehicleType}
                onChange={(e) => setFilters(prev => ({ ...prev, vehicleType: e.target.value }))}
              >
                <option value="all">All Vehicles</option>
                <option value="Sedan">Sedan</option>
                <option value="SUV">SUV</option>
                <option value="Innova">Innova</option>
              </select>
              <button
                className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium ${
                  filters.hasInfants
                    ? 'bg-blue-50 border-blue-300 text-blue-800'
                    : 'bg-white border-gray-300 text-gray-700'
                }`}
                onClick={() => setFilters(prev => ({ ...prev, hasInfants: !prev.hasInfants }))}
              >
                <Baby className="h-4 w-4 mr-2" />
                With Infants
              </button>
              <button
                className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium ${
                  filters.hasGrandparents
                    ? 'bg-blue-50 border-blue-300 text-blue-800'
                    : 'bg-white border-gray-300 text-gray-700'
                }`}
                onClick={() => setFilters(prev => ({ ...prev, hasGrandparents: !prev.hasGrandparents }))}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                With Grandparents
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Family Types Table */}
      <div className="p-6">
        {isLoadingFamilyTypes ? (
          <div className="text-center py-8">
            <p className="text-gray-500">Loading family types...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Composition</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package Cost</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Per Person</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredFamilyTypes.map((type, index) => (
                  <tr key={type.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {type.family_type || type.name}
                          </div>
                          <div className="text-sm text-gray-500">{type.id}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {(type.no_of_adults || type.adults || 0)} Adults
                        {((type.no_of_children || 0) + (type.no_of_child || 0) + (type.children || 0)) > 0 &&
                          `, ${(type.no_of_children || 0) + (type.no_of_child || 0) + (type.children || 0)} Children`}
                        {(type.no_of_infants || type.infants || 0) > 0 &&
                          `, ${(type.no_of_infants || type.infants || 0)} Infants`}
                        {(type.grandparents || 0) > 0 &&
                          `, ${type.grandparents} Grandparents`}
                      </div>
                      <div className="text-sm text-gray-500">
                        Total: {type.family_count || type.totalCount || 0} members
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {type.cab_type || type.cabType || 'Standard'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {type.packageBreakdown?.roomsNeeded || type.rooms_need || type.rooms || 1}
                      {type.packageBreakdown && type.packageBreakdown.extraAdultsCharged > 0 && (
                        <div className="text-xs text-orange-600">
                          +{type.packageBreakdown.extraAdultsCharged} extra adults
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {type.packageCost ? (
                        <div>
                          <div className="text-sm font-medium text-green-900">
                            {formatPrice(type.packageCost)}
                          </div>
                          <div className="text-xs text-green-600">Package Cost</div>
                        </div>
                      ) : type.calculatedPrice ? (
                        <div>
                          <div className="text-sm font-medium text-blue-900">
                            {formatPrice(type.calculatedPrice)}
                          </div>
                          <div className="text-xs text-blue-600">Estimated</div>
                        </div>
                      ) : (
                        <div className="text-sm text-gray-400">Not calculated</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {(type.packageCost || type.calculatedPrice) && (type.family_count || type.totalCount) > 0 ?
                        formatPrice(Math.round((type.packageCost || type.calculatedPrice || 0) / (type.family_count || type.totalCount || 1))) :
                        '-'
                      }
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {type.packageCost ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Calculated
                        </span>
                      ) : type.calculatedPrice ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Estimated
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          Pending
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>

    {/* Detailed Package Breakdown Section */}
    {showPackageCalculation && familyTypes.some(ft => ft.packageCost) && (
      <div className="p-6 border-t border-gray-200">
        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-600" />
          Detailed Package Cost Breakdown
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {familyTypes
            .filter(ft => ft.packageCost && ft.packageBreakdown)
            .slice(0, 6) // Show top 6 for detailed view
            .map((type) => (
              <div key={type.id} className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="mb-3">
                  <h5 className="font-medium text-green-900">{type.family_type || type.name}</h5>
                  <p className="text-sm text-green-700">
                    {(type.no_of_adults || type.adults || 0)}A
                    {((type.no_of_children || 0) + (type.no_of_child || 0) + (type.children || 0)) > 0 &&
                      `, ${(type.no_of_children || 0) + (type.no_of_child || 0) + (type.children || 0)}C`}
                    {(type.no_of_infants || type.infants || 0) > 0 &&
                      `, ${(type.no_of_infants || type.infants || 0)}I`}
                  </p>
                </div>

                {type.packageBreakdown && (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-green-700">Room Cost:</span>
                      <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.roomCost)}</span>
                    </div>
                    {type.packageBreakdown.extraAdultCost > 0 && (
                      <div className="flex justify-between">
                        <span className="text-green-700">Extra Adults:</span>
                        <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.extraAdultCost)}</span>
                      </div>
                    )}
                    {type.packageBreakdown.childrenCost > 0 && (
                      <div className="flex justify-between">
                        <span className="text-green-700">Children (6-12):</span>
                        <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.childrenCost)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-green-700">Vehicle:</span>
                      <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.vehicleCost)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-700">Additional:</span>
                      <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.additionalCosts)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-700">Commission:</span>
                      <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.commission)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-700">GST:</span>
                      <span className="font-medium text-green-900">{formatPrice(type.packageBreakdown.gst)}</span>
                    </div>
                    <div className="border-t border-green-300 pt-2 mt-2">
                      <div className="flex justify-between font-semibold">
                        <span className="text-green-800">Total Package:</span>
                        <span className="text-green-900">{formatPrice(type.packageCost || 0)}</span>
                      </div>
                      <div className="flex justify-between text-xs mt-1">
                        <span className="text-green-600">Per Person:</span>
                        <span className="text-green-700">
                          {(type.family_count || type.totalCount) > 0 ?
                            formatPrice(Math.round((type.packageCost || 0) / (type.family_count || type.totalCount || 1))) :
                            '-'
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
        </div>
      </div>
    )}

    {/* Package Cost Table View */}
    {showPackageCalculation && familyTypes.some(ft => ft.packageCost) && (
      <div className="p-6 border-t border-gray-200">
        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <Package className="h-5 w-5 text-green-600" />
          Package Cost Comparison Table
        </h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-green-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Composition</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room Cost</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle Cost</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Additional</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Package</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Per Person</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {familyTypes
                .filter(ft => ft.packageCost)
                .sort((a, b) => (a.packageCost || 0) - (b.packageCost || 0))
                .map((type) => (
                <tr key={type.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{type.name}</div>
                    <div className="text-sm text-gray-500">{type.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.adults || type.no_of_adults}A
                    {(type.children || type.no_of_children || type.no_of_child) > 0 && `, ${(type.children || type.no_of_children || type.no_of_child)}C`}
                    {(type.infants || type.no_of_infants) > 0 && `, ${(type.infants || type.no_of_infants)}I`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.packageBreakdown?.roomsNeeded || type.rooms || type.rooms_need || 1}
                    {type.packageBreakdown && type.packageBreakdown.extraAdultsCharged > 0 && (
                      <span className="text-xs text-orange-600 ml-1">
                        (+{type.packageBreakdown.extraAdultsCharged} extra)
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.packageBreakdown ? formatPrice(type.packageBreakdown.roomCost + type.packageBreakdown.extraAdultCost + type.packageBreakdown.childrenCost) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.packageBreakdown ? formatPrice(type.packageBreakdown.vehicleCost) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.packageBreakdown ? formatPrice(type.packageBreakdown.additionalCosts + type.packageBreakdown.commission + type.packageBreakdown.gst) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-green-900">
                      {formatPrice(type.packageCost || 0)}
                    </div>
                    {type.packageBreakdown && type.packageBreakdown.childrenCharged > 0 && (
                      <div className="text-xs text-green-600">
                        {type.packageBreakdown.childrenCharged} children charged
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.totalCount > 0 ? formatPrice(Math.round((type.packageCost || 0) / type.totalCount)) : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    )}

    {/* Basic Pricing Table View */}
    {showPriceCalculation && familyTypes.some(ft => ft.calculatedPrice) && (
      <div className="p-6 border-t border-gray-200">
        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-blue-600" />
          Basic Price Comparison Table
        </h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Family Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Composition</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hotel Cost</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Other Costs</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price per Person</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {familyTypes
                .filter(ft => ft.calculatedPrice)
                .sort((a, b) => (a.calculatedPrice || 0) - (b.calculatedPrice || 0))
                .map((type) => (
                <tr key={type.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{type.name}</div>
                    <div className="text-sm text-gray-500">{type.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.adults}A{type.children > 0 && `, ${type.children}C`}{type.infants > 0 && `, ${type.infants}I`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.priceBreakdown?.roomsNeeded || type.rooms || type.rooms_need || 1}
                    {type.priceBreakdown && type.priceBreakdown.extraAdults > 0 && (
                      <span className="text-xs text-orange-600 ml-1">
                        (+{type.priceBreakdown.extraAdults} extra)
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.priceBreakdown ? formatPrice(type.priceBreakdown.hotelCost) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.priceBreakdown ? formatPrice(type.priceBreakdown.otherCosts + type.priceBreakdown.commission + type.priceBreakdown.gst) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {formatPrice(type.calculatedPrice || 0)}
                    </div>
                    {type.priceBreakdown && type.priceBreakdown.childrenCharged > 0 && (
                      <div className="text-xs text-blue-600">
                        {type.priceBreakdown.childrenCharged} children charged
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {type.totalCount > 0 ? formatPrice(Math.round((type.calculatedPrice || 0) / type.totalCount)) : '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    )}

    {/* Footer */}
    <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t border-gray-200 flex justify-between items-center">
      <p className="text-sm text-gray-500">
        {filteredFamilyTypes.length} family types available
        {showPackageCalculation && ` • Package costs calculated for ${familyTypes.filter(t => t.packageCost).length} types`}
        {showPriceCalculation && !showPackageCalculation && ` • Basic prices calculated for ${familyTypes.filter(t => t.calculatedPrice).length} types`}
      </p>

      <div className="flex gap-2">
        <button
          type="button"
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => {
            setSelectedFamily('');
            setSearchQuery('');
            setFilters({
              maxMembers: 'all',
              vehicleType: 'all',
              hasInfants: false,
              hasGrandparents: false
            });
          }}
        >
          Reset
        </button>
        <button
          type="button"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          disabled={!selectedFamily}
        >
          Save Selection
        </button>
      </div>
    </div>
  </div>
</>